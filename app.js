// UK Engineering Kitchen Equipments Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Navigation functionality
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav__link');
    
    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        });
    });
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Header scroll effect
    const header = document.querySelector('.header');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = 'none';
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Active navigation link highlighting
    const sections = document.querySelectorAll('section[id]');
    
    function highlightActiveSection() {
        const scrollPosition = window.scrollY + 200;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            const navLink = document.querySelector(`.nav__link[href="#${sectionId}"]`);
            
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                navLinks.forEach(link => link.classList.remove('active'));
                if (navLink) {
                    navLink.classList.add('active');
                }
            }
        });
    }
    
    window.addEventListener('scroll', highlightActiveSection);
    
    // Quote form handling
    const quoteForm = document.getElementById('quote-form');
    
    if (quoteForm) {
        quoteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(quoteForm);
            const name = formData.get('name');
            const phone = formData.get('phone');
            const email = formData.get('email');
            const service = formData.get('service');
            const message = formData.get('message');
            
            // Basic validation
            if (!name || !phone || !email || !service) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }
            
            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showNotification('Please enter a valid email address.', 'error');
                return;
            }
            
            // Phone validation (basic)
            const phoneRegex = /^[0-9]{10,}$/;
            if (!phoneRegex.test(phone.replace(/\D/g, ''))) {
                showNotification('Please enter a valid phone number.', 'error');
                return;
            }
            
            // Simulate form submission
            const submitButton = quoteForm.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            
            submitButton.textContent = 'Sending...';
            submitButton.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                // Create detailed email body
                const emailBody = `
New Quote Request from UK Engineering Kitchen Equipments Website

Customer Details:
==================
Name: ${name}
Phone: ${phone}
Email: ${email}
Service Required: ${service}

Message:
========
${message || 'No additional message provided'}

Request Date: ${new Date().toLocaleString()}

Please contact the customer as soon as possible to discuss their requirements.

Best regards,
UK Engineering Kitchen Equipments Website
                `;

                // Create mailto link to UK Engineering
                const mailtoLink = `mailto:<EMAIL>?subject=New Quote Request from ${name} - ${service}&body=${encodeURIComponent(emailBody)}`;

                // Also create a copy for the customer
                const customerEmailBody = `
Dear ${name},

Thank you for your interest in UK Engineering Kitchen Equipments!

We have received your quote request for: ${service}

Our team will contact you shortly at ${phone} to discuss your requirements in detail.

Your Request Details:
- Service: ${service}
- Message: ${message || 'No additional message'}

Contact Information:
- Pasupathy: 8220749453
- Kumar: 9841470704
- Email: <EMAIL>

Best regards,
UK Engineering Kitchen Equipments Team
144/7, Vellalar Street, Athipet, Ambattur, Chennai - 600058
                `;

                // Open email client for UK Engineering
                window.open(mailtoLink, '_blank');

                // Also prepare customer confirmation email
                const customerMailto = `mailto:${email}?subject=Quote Request Confirmation - UK Engineering Kitchen Equipments&body=${encodeURIComponent(customerEmailBody)}`;

                // Reset form
                quoteForm.reset();

                // Reset button
                submitButton.textContent = originalText;
                submitButton.disabled = false;

                showNotification('Quote request sent successfully! We will contact you soon.', 'success');

                // Optional: Open customer confirmation email after a delay
                setTimeout(() => {
                    if (confirm('Would you like to send yourself a confirmation email?')) {
                        window.open(customerMailto, '_blank');
                    }
                }, 2000);
            }, 1000);
        });
    }
    
    // Notification system
    function showNotification(message, type = 'info') {
        // Remove existing notification
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification--${type}`;
        notification.innerHTML = `
            <div class="notification__content">
                <span class="notification__message">${message}</span>
                <button class="notification__close">&times;</button>
            </div>
        `;
        
        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#dc2626' : type === 'success' ? '#059669' : '#0ea5e9'};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;
        
        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            .notification__content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 12px;
            }
            
            .notification__close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                font-size: 20px;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                transition: background-color 0.2s;
            }
            
            .notification__close:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(notification);
        
        // Close notification
        const closeBtn = notification.querySelector('.notification__close');
        closeBtn.addEventListener('click', () => {
            notification.remove();
        });
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    // Call to action buttons
    const ctaButtons = document.querySelectorAll('[href="#contact"]');
    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const contactSection = document.querySelector('#contact');
            if (contactSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = contactSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Focus on the first form field
                setTimeout(() => {
                    const firstInput = document.querySelector('#name');
                    if (firstInput) {
                        firstInput.focus();
                    }
                }, 1000);
            }
        });
    });
    
    // Phone number click tracking
    const phoneLinks = document.querySelectorAll('a[href^="tel:"]');
    phoneLinks.forEach(link => {
        link.addEventListener('click', function() {
            console.log('Phone number clicked:', this.href);
        });
    });
    
    // Email click tracking
    const emailLinks = document.querySelectorAll('a[href^="mailto:"]');
    emailLinks.forEach(link => {
        link.addEventListener('click', function() {
            console.log('Email clicked:', this.href);
        });
    });
    
    // Scroll to top functionality
    function createScrollToTopButton() {
        const scrollBtn = document.createElement('button');
        scrollBtn.innerHTML = '↑';
        scrollBtn.className = 'scroll-to-top';
        scrollBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #1e3a8a;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        `;
        
        scrollBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        document.body.appendChild(scrollBtn);
        
        // Show/hide scroll button
        window.addEventListener('scroll', function() {
            if (window.scrollY > 500) {
                scrollBtn.style.opacity = '1';
                scrollBtn.style.visibility = 'visible';
            } else {
                scrollBtn.style.opacity = '0';
                scrollBtn.style.visibility = 'hidden';
            }
        });
    }
    
    createScrollToTopButton();
    
    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe cards and sections
    const animateElements = document.querySelectorAll('.service__card, .product__card, .contact__item, .stat');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'all 0.6s ease-out';
        observer.observe(el);
    });
    
    // Preload hero and about images
    const heroImages = [
        'https://pplx-res.cloudinary.com/image/upload/v1752067942/pplx_project_search_images/79bc937827789aacdb76c317611a920b3fce59a9.jpg',
        'https://pplx-res.cloudinary.com/image/upload/v1752067942/pplx_project_search_images/98c17b2c8efb23405286ae97f440d6a776eb3752.jpg'
    ];

    heroImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });

    // Load gallery images dynamically
    loadGalleryImages();
    
    // Performance optimization
    let ticking = false;
    
    function updateScroll() {
        highlightActiveSection();
        ticking = false;
    }
    
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScroll);
            ticking = true;
        }
    }
    
    window.addEventListener('scroll', requestTick);
    
    // Initialize
    console.log('UK Engineering Kitchen Equipments website loaded successfully!');
});

// Modal functionality for image gallery
function openModal(imageSrc, caption) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const modalCaption = document.getElementById('modalCaption');

    modal.style.display = 'block';
    modalImage.src = imageSrc;
    modalCaption.textContent = caption;

    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
}

function closeModal() {
    const modal = document.getElementById('imageModal');
    modal.style.display = 'none';

    // Restore body scroll
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside the image
document.addEventListener('click', function(event) {
    const modal = document.getElementById('imageModal');
    if (event.target === modal) {
        closeModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
    }
});

// Dynamic Gallery Loading System
async function loadGalleryImages() {
    const galleryGrid = document.getElementById('galleryGrid');
    const galleryLoading = document.getElementById('galleryLoading');
    const galleryError = document.getElementById('galleryError');

    if (!galleryGrid || !galleryLoading || !galleryError) {
        console.error('Gallery elements not found');
        return;
    }

    try {
        let images = [];

        // First, try to load from configuration file
        const configImages = await loadFromConfig();
        if (configImages.length > 0) {
            images = configImages;
            console.log('Loaded images from configuration file:', configImages.length);
        } else {
            // Auto-detect local images
            const localImages = await loadLocalImages();
            if (localImages.length > 0) {
                images = localImages;
                console.log('Auto-detected local images:', localImages.length);
            } else {
                throw new Error('No images found in img folder');
            }
        }

        if (images.length > 0) {
            displayImages(images, galleryGrid);
            galleryLoading.style.display = 'none';
            console.log(`Successfully loaded ${images.length} images`);
        } else {
            throw new Error('No images to display');
        }
    } catch (error) {
        console.error('Error loading gallery images:', error);
        galleryLoading.style.display = 'none';
        galleryError.style.display = 'block';
        galleryError.innerHTML = `<p>Unable to load gallery images: ${error.message}</p>`;
    }
}

// Load images from local img folder
async function loadLocalImages() {
    const images = [];

    // Known image patterns in your folder - updated with all available images
    const imageList = [
        '0.png', '1.png', '2.png', '3.png', '4.png',
        'PHOTO-2019-07-06-17-31-54_9.jpg',
        'PHOTO-2019-07-06-17-31-54_10.jpg',
        'PHOTO-2019-07-06-17-31-54_14.jpg',
        'PHOTO-2019-07-06-17-31-54_15.jpg',
        'PHOTO-2019-07-06-17-31-54_16.jpg',
        'PHOTO-2019-07-06-17-31-54_17.jpg',
        'PHOTO-2019-07-06-17-31-54_18.jpg',
        'PHOTO-2019-07-06-17-31-54_19.jpg',
        'PHOTO-2019-07-06-17-31-54_20.jpg',
        'PHOTO-2019-07-06-17-31-54_21.jpg',
        'PHOTO-2019-07-06-17-31-54_22.jpg',
        'PHOTO-2019-07-06-18-29-25.jpg',
        'PHOTO-2019-07-06-18-29-25_1.jpg',
        'PHOTO-2019-07-06-18-29-25_3.jpg',
        'PHOTO-2019-07-06-18-32-41.jpg'
    ];

    // Check each image and add if it exists
    for (let i = 0; i < imageList.length; i++) {
        const imagePath = `img/${imageList[i]}`;
        try {
            const exists = await checkImageExists(imagePath);
            if (exists) {
                images.push({
                    src: imagePath,
                    alt: `UK Engineering Kitchen Equipment Project ${i + 1}`,
                    caption: `Commercial Kitchen Equipment Installation - Project ${i + 1}`
                });
            }
        } catch (e) {
            // Image doesn't exist, continue
            console.log(`Image not found: ${imagePath}`);
        }
    }

    // Also try to auto-detect numbered images (for future additions)
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    for (let i = 5; i < 100; i++) { // Start from 5 since 0-4 are already covered
        for (const ext of imageExtensions) {
            const imagePath = `img/${i}.${ext}`;
            try {
                const exists = await checkImageExists(imagePath);
                if (exists) {
                    images.push({
                        src: imagePath,
                        alt: `UK Engineering Kitchen Equipment Project ${images.length + 1}`,
                        caption: `Commercial Kitchen Equipment Project ${images.length + 1}`
                    });
                    break; // Found image with this number, move to next
                }
            } catch (e) {
                // Image doesn't exist, continue
            }
        }
    }

    return images;
}

// Check if image exists
function checkImageExists(imageSrc) {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = imageSrc;
    });
}

// Future: Google Drive integration can be added here if needed

// Display images in the gallery
function displayImages(images, container) {
    container.innerHTML = '';

    if (images.length === 0) {
        container.innerHTML = `
            <div class="gallery__error">
                <p>No images found. Please check the img folder.</p>
            </div>
        `;
        return;
    }

    images.forEach((image, index) => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery__item';
        galleryItem.onclick = () => openModal(image.src, image.caption);

        galleryItem.innerHTML = `
            <img src="${image.src}" alt="${image.alt}" class="gallery__img" loading="lazy" 
                 onload="this.parentElement.classList.add('loaded')" 
                 onerror="this.parentElement.innerHTML='<div class=\\'gallery__error\\'><p>Image failed to load</p></div>'">
            <div class="gallery__overlay">
                <span class="gallery__text">${image.caption}</span>
            </div>
        `;

        container.appendChild(galleryItem);

        // Add animation delay for staggered loading effect
        setTimeout(() => {
            galleryItem.style.opacity = '1';
            galleryItem.style.transform = 'translateY(0)';
        }, index * 100);
    });

    console.log(`Displayed ${images.length} images in gallery`);
}

// Load from a JSON configuration file
async function loadFromConfig() {
    try {
        const response = await fetch('gallery-config.json');
        const config = await response.json();

        // Store config globally for other functions to use
        window.galleryConfig = config;

        return config.images || [];
    } catch (error) {
        console.log('No gallery config file found, using auto-detection');
        return [];
    }
}

// Enhanced contact tracking
function trackContact(type, value) {
    console.log(`Contact initiated: ${type} - ${value}`);

    // You can add analytics tracking here
    if (typeof gtag !== 'undefined') {
        gtag('event', 'contact', {
            'event_category': 'engagement',
            'event_label': type,
            'value': value
        });
    }
}

// Update phone and email click tracking
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced phone tracking
    const phoneLinks = document.querySelectorAll('a[href^="tel:"]');
    phoneLinks.forEach(link => {
        link.addEventListener('click', function() {
            trackContact('phone', this.href.replace('tel:', ''));
        });
    });

    // Enhanced email tracking
    const emailLinks = document.querySelectorAll('a[href^="mailto:"]');
    emailLinks.forEach(link => {
        link.addEventListener('click', function() {
            trackContact('email', this.href.replace('mailto:', ''));
        });
    });

    // WhatsApp tracking
    const whatsappLinks = document.querySelectorAll('a[href*="wa.me"]');
    whatsappLinks.forEach(link => {
        link.addEventListener('click', function() {
            trackContact('whatsapp', 'message_sent');
        });
    });
});

// Business hours display
function displayBusinessHours() {
    const now = new Date();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const hour = now.getHours();

    // Business hours: Monday to Sunday 9 AM to 10 PM
    const isBusinessDay = day >= 1 && day <= 7; // All days
    const isBusinessHour = hour >= 9 && hour < 22; // 9 AM to 10 PM

    const isOpen = isBusinessDay && isBusinessHour;

    // Create business hours indicator
    const indicator = document.createElement('div');
    indicator.className = `business-hours ${isOpen ? 'open' : 'closed'}`;
    indicator.innerHTML = `
        <span class="status-dot"></span>
        <span class="status-text">${isOpen ? 'Open Now' : 'Closed'}</span>
        <span class="hours-text">Mon-Sun: 9 AM - 10 PM</span>
    `;

    // Add styles
    indicator.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        background: ${isOpen ? '#059669' : '#dc2626'};
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    `;

    const statusDot = indicator.querySelector('.status-dot');
    if (statusDot) {
        statusDot.style.cssText = `
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: ${isOpen ? 'pulse 2s infinite' : 'none'};
        `;
    }

    document.body.appendChild(indicator);
}

// Initialize business hours display
document.addEventListener('DOMContentLoaded', displayBusinessHours);

// FAQ Toggle Functionality
function toggleFAQ(element) {
    const faqItem = element.parentElement;
    const isActive = faqItem.classList.contains('active');

    // Close all FAQ items
    document.querySelectorAll('.faq__item').forEach(item => {
        item.classList.remove('active');
    });

    // Open clicked item if it wasn't active
    if (!isActive) {
        faqItem.classList.add('active');
    }
}

// Quick Inquiry Form Handler
document.addEventListener('DOMContentLoaded', function() {
    const quickForm = document.getElementById('quick-form');

    if (quickForm) {
        quickForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(quickForm);
            const name = formData.get('name');
            const phone = formData.get('phone');
            const businessType = formData.get('business_type');
            const location = formData.get('location');
            const requirements = formData.get('requirements');

            // Validation
            if (!name || !phone || !businessType || !location) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Phone validation
            const phoneRegex = /^[0-9]{10,}$/;
            if (!phoneRegex.test(phone.replace(/\D/g, ''))) {
                showNotification('Please enter a valid phone number.', 'error');
                return;
            }

            // Create detailed email for quick inquiry
            const emailBody = `
QUICK INQUIRY - UK Engineering Kitchen Equipments

Customer Information:
====================
Name: ${name}
Phone: ${phone}
Business Type: ${businessType}
Location: ${location}

Requirements:
=============
${requirements || 'No specific requirements mentioned'}

Inquiry Date: ${new Date().toLocaleString()}
Source: Website Quick Inquiry Form

PRIORITY: Quick Quote Request - Respond within 24 hours

Contact Details:
- Phone: ${phone}
- Preferred Contact: Phone Call
- Business Type: ${businessType}
- Location: ${location}

Please contact this customer promptly for a personalized quote.

Best regards,
UK Engineering Kitchen Equipments Website
            `;

            // Create mailto link
            const mailtoLink = `mailto:<EMAIL>?subject=QUICK INQUIRY - ${businessType} Kitchen Setup - ${name}&body=${encodeURIComponent(emailBody)}`;

            // Open email client
            window.open(mailtoLink, '_blank');

            // Reset form
            quickForm.reset();

            // Show success message
            showNotification('Quick inquiry sent! We will contact you within 24 hours.', 'success');

            // Track the inquiry
            console.log('Quick inquiry submitted:', {
                name,
                phone,
                businessType,
                location,
                timestamp: new Date().toISOString()
            });
        });
    }
});

// Enhanced Analytics Tracking
function trackUserInteraction(action, category, label) {
    console.log(`User Interaction: ${action} - ${category} - ${label}`);

    // Google Analytics tracking (if implemented)
    if (typeof gtag !== 'undefined') {
        gtag('event', action, {
            'event_category': category,
            'event_label': label,
            'value': 1
        });
    }
}

// Track FAQ interactions
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.faq__question').forEach(question => {
        question.addEventListener('click', function() {
            const questionText = this.querySelector('h3').textContent;
            trackUserInteraction('faq_click', 'engagement', questionText);
        });
    });
});

// Track scroll depth for SEO insights
let maxScrollDepth = 0;
window.addEventListener('scroll', function() {
    const scrollDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;

        // Track milestone scroll depths
        if (maxScrollDepth >= 25 && maxScrollDepth < 50) {
            trackUserInteraction('scroll_depth', 'engagement', '25_percent');
        } else if (maxScrollDepth >= 50 && maxScrollDepth < 75) {
            trackUserInteraction('scroll_depth', 'engagement', '50_percent');
        } else if (maxScrollDepth >= 75 && maxScrollDepth < 100) {
            trackUserInteraction('scroll_depth', 'engagement', '75_percent');
        } else if (maxScrollDepth >= 100) {
            trackUserInteraction('scroll_depth', 'engagement', '100_percent');
        }
    }
});