
:root {
  /* Professional Kitchen Equipment Theme Colors */
  --color-background: #fafbfc;
  --color-surface: #ffffff;
  --color-surface-light: #f8fafc;
  --color-surface-gray: #f1f5f9;
  --color-text: #1a202c;
  --color-text-primary: #0f1419;
  --color-text-secondary: #4a5568;
  --color-text-muted: #718096;
  --color-primary: #2b6cb0;
  --color-primary-hover: #2c5282;
  --color-primary-active: #1a365d;
  --color-secondary: #e2e8f0;
  --color-secondary-hover: #cbd5e1;
  --color-secondary-active: #a0aec0;
  --color-border: #e2e8f0;
  --color-border-light: #f7fafc;
  --color-btn-primary-text: #ffffff;
  --color-card-border: #e2e8f0;
  --color-card-border-inner: #f7fafc;
  --color-error: #e53e3e;
  --color-success: #38a169;
  --color-warning: #d69e2e;
  --color-info: #3182ce;
  --color-focus-ring: rgba(43, 108, 176, 0.4);
  --color-select-caret: #4a5568;

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgb(63, 67, 67);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  box-shadow: 0 2px 4px rgba(43, 108, 176, 0.2);
  transition: all var(--duration-fast) var(--ease-standard);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  box-shadow: 0 4px 8px rgba(43, 108, 176, 0.3);
  transform: translateY(-1px);
}

.btn--primary:active {
  background: var(--color-primary-active);
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(43, 108, 176, 0.2);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Additional styles for UK Engineering Kitchen Equipments website */

/* Clean White Theme - Professional Kitchen Equipment Company */
:root {
  --color-primary: #1e3a8a; /* Keep the professional blue */
  --color-primary-hover: #1d4ed8;
  --color-primary-active: #1e40af;
  --color-accent: #3b82f6; /* Bright blue accent */
  --color-dark: #0f172a; /* Very dark for headings */
  --color-light: #ffffff; /* Pure white */
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid var(--color-border);
  z-index: 1000;
  transition: all var(--duration-normal) var(--ease-standard);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-16) 0;
}

.nav__brand {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
}

.nav__logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin: 0;
}

.nav__tagline {
  font-size: var(--font-size-sm);
  color: var(--color-secondary);
  font-weight: var(--font-weight-medium);
}

.nav__menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-32);
}

.nav__item {
  position: relative;
}

.nav__link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  padding: var(--space-8) 0;
  transition: color var(--duration-fast) var(--ease-standard);
}

.nav__link:hover {
  color: var(--color-primary);
}

.nav__link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: width var(--duration-fast) var(--ease-standard);
}

.nav__link:hover::after {
  width: 100%;
}

.nav__contact {
  display: flex;
  align-items: center;
}

.nav__toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: var(--space-4);
}

.nav__toggle span {
  width: 24px;
  height: 2px;
  background: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #ffffff 0%, var(--color-surface-light) 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.hero__title {
  font-size: var(--font-size-4xl);
  line-height: 1.1;
  margin-bottom: var(--space-24);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-bold);
}

.hero__description {
  font-size: var(--font-size-xl);
  line-height: 1.6;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-32);
  font-weight: var(--font-weight-medium);
}

.hero__buttons {
  display: flex;
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.hero__features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-20);
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.feature__icon {
  width: 20px;
  height: 20px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

.feature__text {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.hero__image {
  position: relative;
}

.hero__img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

/* Section Headers */
.section__header {
  text-align: center;
  margin-bottom: var(--space-32);
}

.section__title {
  font-size: var(--font-size-3xl);
  color: var(--color-text-primary);
  margin-bottom: var(--space-16);
  font-weight: var(--font-weight-bold);
  letter-spacing: var(--letter-spacing-tight);
}

.section__description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
  font-weight: var(--font-weight-medium);
  line-height: 1.6;
}

/* About Section */
.about {
  padding: 80px 0;
  background: var(--color-gray-50);
}

.about__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.about__text p {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  color: var(--color-gray-700);
  margin-bottom: var(--space-20);
  font-weight: var(--font-weight-medium);
}

.about__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-24);
  margin-top: var(--space-32);
}

.stat {
  text-align: center;
  padding: var(--space-20);
  background: var(--color-light);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.stat__number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-8);
}

.stat__text {
  color: var(--color-gray-700);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-md);
}

.about__img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

/* Services Section */
.services {
  padding: 80px 0;
  background: var(--color-background);
}

.services__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-24);
}

.service__card {
  background: var(--color-surface);
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  text-align: center;
  border: 1px solid var(--color-border);
  transition: transform var(--duration-normal) var(--ease-standard),
              box-shadow var(--duration-normal) var(--ease-standard);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.service__card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: var(--color-primary);
}

.service__icon {
  font-size: 48px;
  margin-bottom: var(--space-16);
  color: var(--color-primary);
}

.service__title {
  font-size: var(--font-size-xl);
  color: var(--color-text-primary);
  margin-bottom: var(--space-16);
  font-weight: var(--font-weight-bold);
}

.service__description {
  color: var(--color-text-secondary);
  line-height: 1.6;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
}

/* Products Section */
.products {
  padding: 80px 0;
  background: var(--color-gray-50);
}

.products__categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-20);
  margin-bottom: var(--space-32);
}

.category {
  background: var(--color-primary);
  color: white;
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.category__title {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-bold);
}

.category__description {
  opacity: 1;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  line-height: 1.5;
}

.products__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
}

.product__card {
  background: var(--color-background);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.product__card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.product__name {
  font-size: var(--font-size-xl);
  color: var(--color-text-primary);
  margin-bottom: var(--space-16);
  padding-bottom: var(--space-8);
  border-bottom: 2px solid var(--color-primary);
  font-weight: var(--font-weight-bold);
}

.product__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product__list li {
  padding: var(--space-8) 0;
  color: var(--color-text-secondary);
  border-bottom: 1px solid var(--color-border);
  position: relative;
  padding-left: var(--space-20);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
}

.product__list li:before {
  content: '•';
  color: var(--color-primary);
  position: absolute;
  left: 0;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
}

.product__list li:last-child {
  border-bottom: none;
}

/* Contact Section */
.contact {
  padding: 80px 0;
  background: var(--color-gray-50);
}

.contact__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
}

.contact__info {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

.contact__item {
  background: var(--color-surface);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.contact__title {
  font-size: var(--font-size-xl);
  color: var(--color-text-primary);
  margin-bottom: var(--space-16);
}

.contact__details p {
  margin-bottom: var(--space-8);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-md);
}

.contact__details a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.contact__details a:hover {
  text-decoration: underline;
}

.contact__form {
  background: var(--color-surface);
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.form__title {
  font-size: var(--font-size-2xl);
  color: var(--color-text-primary);
  margin-bottom: var(--space-24);
  text-align: center;
}

/* Footer */
.footer {
  background: var(--color-gray-900);
  color: white;
  padding: 60px 0 20px;
}

.footer__content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-32);
  margin-bottom: var(--space-32);
}

.footer__title {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-16);
  color: var(--color-accent);
}

.footer__subtitle {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-16);
  color: var(--color-accent);
}

.footer__description {
  color: #cbd5e1;
  line-height: 1.6;
  margin-bottom: var(--space-16);
}

.footer__contact p {
  margin-bottom: var(--space-8);
  color: #cbd5e1;
}

.footer__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__list li {
  margin-bottom: var(--space-8);
}

.footer__list a {
  color: #cbd5e1;
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.footer__list a:hover {
  color: var(--color-accent);
}

.footer__bottom {
  text-align: center;
  padding-top: var(--space-20);
  border-top: 1px solid #334155;
  color: #94a3b8;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero__container {
    grid-template-columns: 1fr;
    gap: var(--space-24);
    text-align: center;
  }
  
  .about__content {
    grid-template-columns: 1fr;
    gap: var(--space-24);
  }
  
  .contact__content {
    grid-template-columns: 1fr;
    gap: var(--space-24);
  }
  
  .about__stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .nav__menu {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--color-surface);
    flex-direction: column;
    padding: var(--space-20);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--duration-normal) var(--ease-standard);
  }
  
  .nav__menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav__toggle {
    display: flex;
  }
  
  .nav__toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }
  
  .nav__toggle.active span:nth-child(2) {
    opacity: 0;
  }
  
  .nav__toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }
  
  .hero {
    padding: 100px 0 60px;
  }
  
  .hero__title {
    font-size: var(--font-size-3xl);
  }
  
  .hero__description {
    font-size: var(--font-size-lg);
  }
  
  .hero__buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .hero__features {
    justify-content: center;
  }
  
  .about__stats {
    grid-template-columns: 1fr;
  }
  
  .services__grid {
    grid-template-columns: 1fr;
  }
  
  .products__categories {
    grid-template-columns: 1fr;
  }
  
  .products__grid {
    grid-template-columns: 1fr;
  }
  
  .footer__content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .inquiry__content {
    grid-template-columns: 1fr;
    gap: var(--space-24);
    text-align: center;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }
}

@media (max-width: 480px) {
  .hero__title {
    font-size: var(--font-size-2xl);
  }
  
  .section__title {
    font-size: var(--font-size-2xl);
  }
  
  .hero__buttons .btn {
    width: 100%;
    text-align: center;
  }
  
  .contact__form {
    padding: var(--space-20);
  }
  
  .service__card {
    padding: var(--space-20);
  }
  
  .product__card {
    padding: var(--space-16);
  }
  
  .contact__item {
    padding: var(--space-16);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom button styles */
.btn--primary {
  background: var(--color-primary);
  color: white;
  border: none;
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  color: white;
}

.btn--outline {
  background: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.btn--outline:hover {
  background: var(--color-primary);
  color: white;
}

/* Form styling */
.form-control {
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-gray-900);
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-control::placeholder {
  color: var(--color-gray-500);
}

/* Animation for cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.service__card,
.product__card,
.contact__item {
  animation: fadeInUp 0.6s ease-out;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s infinite;
}

/* Gallery Section */
.gallery {
  padding: 80px 0;
  background: var(--color-background);
  position: relative;
}

.gallery::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-border), transparent);
}

.gallery__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-24);
  max-width: 1400px;
  margin: 0 auto;
}

.gallery__item {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.gallery__item:hover {
  transform: scale(1.05);
}

.gallery__img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.gallery__item:hover .gallery__img {
  transform: scale(1.1);
}

.gallery__overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: var(--space-20);
  transform: translateY(100%);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.gallery__item:hover .gallery__overlay {
  transform: translateY(0);
}

.gallery__text {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-lg);
}

/* Gallery Loading States */
.gallery__loading,
.gallery__error {
  text-align: center;
  padding: var(--space-32);
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  background: var(--color-surface-light);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.gallery__error {
  color: var(--color-error);
  background: rgba(229, 62, 62, 0.1);
  border-color: rgba(229, 62, 62, 0.2);
}

/* Dynamic Gallery Items */
.gallery__item {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.gallery__item.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Lazy Loading Placeholder */
.gallery__img[loading="lazy"] {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Gallery Grid */
.gallery__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-20);
  min-height: 200px;
}

@media (max-width: 768px) {
  .gallery__grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--space-16);
  }
}

@media (max-width: 480px) {
  .gallery__grid {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  animation: fadeIn 0.3s ease-out;
}

.modal__content {
  position: relative;
  margin: auto;
  padding: var(--space-20);
  width: 90%;
  max-width: 800px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
}

.modal__image {
  width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: var(--radius-lg);
}

.modal__close {
  position: absolute;
  top: 10px;
  right: 25px;
  color: white;
  font-size: 35px;
  font-weight: bold;
  cursor: pointer;
  transition: color var(--duration-fast) var(--ease-standard);
}

.modal__close:hover {
  color: var(--color-primary);
}

.modal__caption {
  color: white;
  font-size: var(--font-size-lg);
  margin-top: var(--space-16);
  font-weight: var(--font-weight-medium);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Category styling */
.category {
  transition: transform var(--duration-normal) var(--ease-standard);
}

.category:hover {
  transform: translateY(-4px);
}

/* WhatsApp Float Button */
.whatsapp-float {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: #25D366;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
  z-index: 1000;
  transition: all var(--duration-normal) var(--ease-standard);
  text-decoration: none;
}

.whatsapp-float:hover {
  background: #128C7E;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
  color: white;
}

.whatsapp-float svg {
  width: 28px;
  height: 28px;
}

/* Business Hours Indicator */
.business-hours {
  font-family: var(--font-family-base);
}

.business-hours .hours-text {
  font-size: 10px;
  opacity: 0.9;
  margin-left: 4px;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 640px) {
  .gallery__grid {
    grid-template-columns: 1fr;
  }

  .gallery__img {
    height: 200px;
  }

  .modal__content {
    width: 95%;
    padding: var(--space-10);
  }

  .modal__image {
    max-height: 60vh;
  }

  .whatsapp-float {
    bottom: 140px;
    right: 15px;
    width: 50px;
    height: 50px;
  }

  .whatsapp-float svg {
    width: 24px;
    height: 24px;
  }

  .business-hours {
    top: 70px;
    right: 10px;
    font-size: 10px;
    padding: 6px 10px;
  }
}

/* Print Styles */
@media print {
  .header,
  .whatsapp-float,
  .business-hours,
  .scroll-to-top,
  .modal {
    display: none !important;
  }

  .hero {
    padding: 20px 0;
    min-height: auto;
  }

  .section {
    padding: 20px 0;
  }

  .gallery__overlay {
    transform: translateY(0);
    position: static;
    background: var(--color-surface);
    color: var(--color-text);
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #000080;
    --color-text: #000000;
    --color-background: #ffffff;
    --color-border: #000000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .gallery__item:hover {
    transform: none;
  }

  .service__card:hover {
    transform: none;
  }
}

/* Focus Improvements for Accessibility */
.gallery__item:focus {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
}

/* Loading States */
.img-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Text Readability - White Theme */
.section__description,
.service__description,
.category__description,
.about__text p,
.contact__details p,
.product__list li {
  text-shadow: none; /* Remove text shadow for clean white theme */
}

/* Ensure all headings are bold and readable */
h1, h2, h3, h4, h5, h6,
.section__title,
.service__title,
.category__title,
.product__name,
.contact__title {
  font-weight: var(--font-weight-bold) !important;
  color: var(--color-gray-900) !important;
}

/* Ensure all body text has good contrast */
p, li, span, div {
  color: var(--color-gray-700);
}

/* Special emphasis for important text */
.hero__description,
.section__description {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
}

/* Clean white theme text hierarchy */
.text-primary {
  color: var(--color-gray-900) !important;
}

.text-secondary {
  color: var(--color-gray-700) !important;
}

.text-muted {
  color: var(--color-gray-500) !important;
}

/* FAQ Section */
.faq {
  padding: 80px 0;
  background: var(--color-background);
}

.faq__grid {
  display: grid;
  gap: var(--space-16);
  max-width: 800px;
  margin: 0 auto;
}

.faq__item {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.faq__item:hover {
  box-shadow: var(--shadow-md);
}

.faq__question {
  padding: var(--space-20);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-surface);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.faq__question:hover {
  background: var(--color-secondary);
}

.faq__question h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--color-gray-900);
  font-weight: var(--font-weight-semibold);
}

.faq__icon {
  font-size: var(--font-size-2xl);
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
  transition: transform var(--duration-fast) var(--ease-standard);
}

.faq__item.active .faq__icon {
  transform: rotate(45deg);
}

.faq__answer {
  padding: 0 var(--space-20);
  max-height: 0;
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.faq__item.active .faq__answer {
  padding: 0 var(--space-20) var(--space-20);
  max-height: 200px;
}

.faq__answer p {
  margin: 0;
  color: var(--color-gray-700);
  line-height: 1.6;
  font-weight: var(--font-weight-medium);
}

/* Quick Inquiry Section */
.quick-inquiry {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: white;
}

.inquiry__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.inquiry__title {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-16);
  color: white;
  font-weight: var(--font-weight-bold);
}

.inquiry__description {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-weight: var(--font-weight-medium);
}

.inquiry__form {
  background: white;
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  margin-bottom: var(--space-16);
}

.quick-form .form-control {
  margin-bottom: var(--space-16);
}

.quick-form .btn {
  margin-top: var(--space-8);
}

/* Bright text for cards and sections with dark backgrounds */

/* Service Cards */
.service__card {
  color: #fff;
}
.service__title,
.service__description {
  color: #fff;
}

/* Product Cards */
.product__card {
  color: #fff;
}
.product__name,
.product__list li {
  color: #fff;
}
.product__list li:before {
  color: #fff;
}

/* Contact Info Cards */
.contact__item {
  color: #fff;
}
.contact__title,
.contact__details p,
.contact__details a {
  color: #fff;
}

/* Gallery Section */
.gallery,
.gallery__item,
.gallery__overlay,
.gallery__text {
  color: #fff;
}

/* FAQ Section */
.faq__item {
  color: #fff;
}
.faq__question,
.faq__answer {
  color: #fff;
}

/* Footer */
.footer {
  color: #fff;
}
.footer__title,
.footer__subtitle,
.footer__description,
.footer__contact p,
.footer__list a,
.footer__list li,
.footer__bottom {
  color: #fff;
}

/* Quick Inquiry Section (if dark background) */
.quick-inquiry,
.inquiry__content,
.inquiry__title,
.inquiry__description {
  color: #fff;
}

/* Ensure links on dark backgrounds are also bright */
.footer__list a,
.contact__details a {
  color: #fff;
  text-decoration: underline;
}
.footer__list a:hover,
.contact__details a:hover {
  color: #ffd700;
}

/* Ensure input and textarea text is bright on dark backgrounds */
.product__card input,
.product__card textarea,
.contact__item input,
.contact__item textarea,
.footer input,
.footer textarea {
  color: #fff;
  background: #222;
  border-color: #444;
}

/* For placeholder text on dark backgrounds */
.product__card input::placeholder,
.product__card textarea::placeholder,
.contact__item input::placeholder,
.contact__item textarea::placeholder,
.footer input::placeholder,
.footer textarea::placeholder {
  color: #ccc;
}

/* FORCE bright text on all dark backgrounds */
.faq, .faq *,
.faq__item, .faq__item *,
.faq__question, .faq__question *,
.faq__answer, .faq__answer *,
.gallery, .gallery *,
.gallery__item, .gallery__item *,
.gallery__overlay, .gallery__overlay *,
.gallery__text, .gallery__text *,
.service__card, .service__card *,
.product__card, .product__card *,
.contact__item, .contact__item *,
.footer, .footer *,
.footer__title, .footer__subtitle, .footer__description, .footer__contact, .footer__list, .footer__bottom,
.quick-inquiry, .quick-inquiry * {
  color: #fff !important;
}

/* Make links on dark backgrounds bold and underlined for visibility */
.faq a, .gallery a, .service__card a, .product__card a, .contact__item a, .footer a, .quick-inquiry a {
  color: #fff !important;
  font-weight: bold;
  text-decoration: underline;
}
.faq a:hover, .gallery a:hover, .service__card a:hover, .product__card a:hover, .contact__item a:hover, .footer a:hover, .quick-inquiry a:hover {
  color: #ffd700 !important;
}

/* === UI FIXES: High Specificity Overrides === */

/* 1. Card backgrounds and text */
.service__card, .product__card, .contact__item, .faq__item {
  background: #23272f !important;
  border: 1px solid #333 !important;
  color: #fff !important;
}
.service__title, .product__name, .contact__title, .faq__question {
  font-weight: bold !important;
  color: #fff !important;
}

/* 2. Get a Quote section and forms */
.contact__form, .quick-form, .inquiry__form {
  background: #fff !important;
  color: #222 !important;
  border: 1px solid #e2e8f0 !important;
}
.contact__form label, .quick-form label, .inquiry__form label {
  color: #222 !important;
  font-weight: 500 !important;
}
.contact__form input, .contact__form textarea, .quick-form input, .quick-form textarea, .inquiry__form input, .inquiry__form textarea {
  background: #f8fafc !important;
  color: #222 !important;
  border: 1px solid #cbd5e1 !important;
}
.contact__form input::placeholder, .contact__form textarea::placeholder, .quick-form input::placeholder, .quick-form textarea::placeholder, .inquiry__form input::placeholder, .inquiry__form textarea::placeholder {
  color: #888 !important;
}
.contact__form button, .quick-form button, .inquiry__form button {
  background: #2b6cb0 !important;
  color: #fff !important;
  font-weight: bold !important;
}

/* 3. Logo tagline */
.nav__tagline {
  color: #b0b6c3 !important;
  font-weight: 500 !important;
}

/* 4. Nav links */
.nav__link {
  color: #23272f !important;
  font-weight: 500 !important;
  transition: color 0.2s;
}
.nav__link.active, .nav__link:hover {
  color: #2b6cb0 !important;
  font-weight: 700 !important;
}

/* Bright section headers/descriptions on dark backgrounds */
.services .section__title,
.services .section__description {
  color: #fff !important;
}

@media (max-width: 600px) {
  .gallery__grid, .faq__grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }
  section, .gallery, .faq, .contact, .footer, .quick-inquiry {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
  .form-control, .btn {
    width: 100% !important;
    box-sizing: border-box;
  }
  .footer__content {
    grid-template-columns: 1fr !important;
    text-align: center !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
@media (max-width: 900px) {
  .nav__toggle {
    display: flex !important;
  }
}
@media (max-width: 600px) {
  .nav__menu {
    font-size: 1.2em;
    gap: 24px !important;
    padding: 24px 0 !important;
  }
  .nav__item {
    margin-bottom: 12px;
  }
}
@media (max-width: 400px) {
  .section__title, .hero__title {
    font-size: 1.3em !important;
  }
}

@media (max-width: 768px) {
  .inquiry__content {
    grid-template-columns: 1fr !important;
    gap: 24px !important;
    text-align: center !important;
  }
  .inquiry__text, .inquiry__form {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto 16px auto !important;
  }
  .inquiry__form form {
    width: 100% !important;
  }
}