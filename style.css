
:root {
  /* Colors */
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Additional styles for UK Engineering Kitchen Equipments website */

/* Custom color scheme for professional kitchen equipment company */
:root {
  --color-primary: #1e3a8a; /* Deep blue */
  --color-primary-hover: #1d4ed8;
  --color-primary-active: #1e40af;
  --color-secondary: #64748b; /* Steel gray */
  --color-secondary-hover: #475569;
  --color-secondary-active: #334155;
  --color-accent: #0ea5e9; /* Light blue accent */
  --color-dark: #0f172a; /* Dark navy */
  --color-light: #f8fafc; /* Off white */
  --color-steel: #94a3b8; /* Steel gray light */
  --color-steel-dark: #475569; /* Steel gray dark */
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  z-index: 1000;
  transition: all var(--duration-normal) var(--ease-standard);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-16) 0;
}

.nav__brand {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
}

.nav__logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin: 0;
}

.nav__tagline {
  font-size: var(--font-size-sm);
  color: var(--color-secondary);
  font-weight: var(--font-weight-medium);
}

.nav__menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-32);
}

.nav__item {
  position: relative;
}

.nav__link {
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  padding: var(--space-8) 0;
  transition: color var(--duration-fast) var(--ease-standard);
}

.nav__link:hover {
  color: var(--color-primary);
}

.nav__link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: width var(--duration-fast) var(--ease-standard);
}

.nav__link:hover::after {
  width: 100%;
}

.nav__contact {
  display: flex;
  align-items: center;
}

.nav__toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: var(--space-4);
}

.nav__toggle span {
  width: 24px;
  height: 2px;
  background: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, var(--color-light) 0%, #e2e8f0 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.hero__title {
  font-size: var(--font-size-4xl);
  line-height: 1.1;
  margin-bottom: var(--space-24);
  color: var(--color-dark);
}

.hero__description {
  font-size: var(--font-size-xl);
  line-height: 1.6;
  color: var(--color-steel-dark);
  margin-bottom: var(--space-32);
}

.hero__buttons {
  display: flex;
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.hero__features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-20);
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.feature__icon {
  width: 20px;
  height: 20px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

.feature__text {
  color: var(--color-steel-dark);
  font-weight: var(--font-weight-medium);
}

.hero__image {
  position: relative;
}

.hero__img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

/* Section Headers */
.section__header {
  text-align: center;
  margin-bottom: var(--space-32);
}

.section__title {
  font-size: var(--font-size-3xl);
  color: var(--color-dark);
  margin-bottom: var(--space-16);
}

.section__description {
  font-size: var(--font-size-lg);
  color: var(--color-steel-dark);
  max-width: 600px;
  margin: 0 auto;
}

/* About Section */
.about {
  padding: 80px 0;
  background: var(--color-surface);
}

.about__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
}

.about__text p {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  color: var(--color-steel-dark);
  margin-bottom: var(--space-20);
}

.about__stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-24);
  margin-top: var(--space-32);
}

.stat {
  text-align: center;
  padding: var(--space-20);
  background: var(--color-light);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.stat__number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-8);
}

.stat__text {
  color: var(--color-steel-dark);
  font-weight: var(--font-weight-medium);
}

.about__img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

/* Services Section */
.services {
  padding: 80px 0;
  background: var(--color-background);
}

.services__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-24);
}

.service__card {
  background: var(--color-surface);
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  text-align: center;
  border: 1px solid var(--color-border);
  transition: transform var(--duration-normal) var(--ease-standard),
              box-shadow var(--duration-normal) var(--ease-standard);
}

.service__card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.service__icon {
  font-size: 48px;
  margin-bottom: var(--space-16);
}

.service__title {
  font-size: var(--font-size-xl);
  color: var(--color-dark);
  margin-bottom: var(--space-16);
}

.service__description {
  color: var(--color-steel-dark);
  line-height: 1.6;
}

/* Products Section */
.products {
  padding: 80px 0;
  background: var(--color-surface);
}

.products__categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-20);
  margin-bottom: var(--space-32);
}

.category {
  background: var(--color-primary);
  color: white;
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  text-align: center;
}

.category__title {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-8);
}

.category__description {
  opacity: 0.9;
  font-size: var(--font-size-sm);
}

.products__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
}

.product__card {
  background: var(--color-background);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.product__name {
  font-size: var(--font-size-xl);
  color: var(--color-dark);
  margin-bottom: var(--space-16);
  padding-bottom: var(--space-8);
  border-bottom: 2px solid var(--color-primary);
}

.product__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product__list li {
  padding: var(--space-8) 0;
  color: var(--color-steel-dark);
  border-bottom: 1px solid var(--color-border);
  position: relative;
  padding-left: var(--space-20);
}

.product__list li:before {
  content: '•';
  color: var(--color-primary);
  position: absolute;
  left: 0;
  font-weight: var(--font-weight-bold);
}

.product__list li:last-child {
  border-bottom: none;
}

/* Contact Section */
.contact {
  padding: 80px 0;
  background: var(--color-background);
}

.contact__content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
}

.contact__info {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

.contact__item {
  background: var(--color-surface);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.contact__title {
  font-size: var(--font-size-xl);
  color: var(--color-dark);
  margin-bottom: var(--space-16);
}

.contact__details p {
  margin-bottom: var(--space-8);
  color: var(--color-steel-dark);
}

.contact__details a {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.contact__details a:hover {
  text-decoration: underline;
}

.contact__form {
  background: var(--color-surface);
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
}

.form__title {
  font-size: var(--font-size-2xl);
  color: var(--color-dark);
  margin-bottom: var(--space-24);
  text-align: center;
}

/* Footer */
.footer {
  background: var(--color-dark);
  color: white;
  padding: 60px 0 20px;
}

.footer__content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-32);
  margin-bottom: var(--space-32);
}

.footer__title {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-16);
  color: var(--color-accent);
}

.footer__subtitle {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-16);
  color: var(--color-accent);
}

.footer__description {
  color: #cbd5e1;
  line-height: 1.6;
  margin-bottom: var(--space-16);
}

.footer__contact p {
  margin-bottom: var(--space-8);
  color: #cbd5e1;
}

.footer__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__list li {
  margin-bottom: var(--space-8);
}

.footer__list a {
  color: #cbd5e1;
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

.footer__list a:hover {
  color: var(--color-accent);
}

.footer__bottom {
  text-align: center;
  padding-top: var(--space-20);
  border-top: 1px solid #334155;
  color: #94a3b8;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero__container {
    grid-template-columns: 1fr;
    gap: var(--space-24);
    text-align: center;
  }
  
  .about__content {
    grid-template-columns: 1fr;
    gap: var(--space-24);
  }
  
  .contact__content {
    grid-template-columns: 1fr;
    gap: var(--space-24);
  }
  
  .about__stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .nav__menu {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--color-surface);
    flex-direction: column;
    padding: var(--space-20);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--duration-normal) var(--ease-standard);
  }
  
  .nav__menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav__toggle {
    display: flex;
  }
  
  .nav__toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }
  
  .nav__toggle.active span:nth-child(2) {
    opacity: 0;
  }
  
  .nav__toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }
  
  .hero {
    padding: 100px 0 60px;
  }
  
  .hero__title {
    font-size: var(--font-size-3xl);
  }
  
  .hero__description {
    font-size: var(--font-size-lg);
  }
  
  .hero__buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .hero__features {
    justify-content: center;
  }
  
  .about__stats {
    grid-template-columns: 1fr;
  }
  
  .services__grid {
    grid-template-columns: 1fr;
  }
  
  .products__categories {
    grid-template-columns: 1fr;
  }
  
  .products__grid {
    grid-template-columns: 1fr;
  }
  
  .footer__content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero__title {
    font-size: var(--font-size-2xl);
  }
  
  .section__title {
    font-size: var(--font-size-2xl);
  }
  
  .hero__buttons .btn {
    width: 100%;
    text-align: center;
  }
  
  .contact__form {
    padding: var(--space-20);
  }
  
  .service__card {
    padding: var(--space-20);
  }
  
  .product__card {
    padding: var(--space-16);
  }
  
  .contact__item {
    padding: var(--space-16);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom button styles */
.btn--primary {
  background: var(--color-primary);
  color: white;
  border: none;
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  color: white;
}

.btn--outline {
  background: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.btn--outline:hover {
  background: var(--color-primary);
  color: white;
}

/* Form styling */
.form-control {
  border: 1px solid var(--color-border);
  background: var(--color-background);
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* Animation for cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.service__card,
.product__card,
.contact__item {
  animation: fadeInUp 0.6s ease-out;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s infinite;
}

/* Gallery Section */
.gallery {
  padding: 80px 0;
  background: var(--color-background);
}

.gallery__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-20);
}

.gallery__item {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.gallery__item:hover {
  transform: scale(1.05);
}

.gallery__img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.gallery__item:hover .gallery__img {
  transform: scale(1.1);
}

.gallery__overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: var(--space-20);
  transform: translateY(100%);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.gallery__item:hover .gallery__overlay {
  transform: translateY(0);
}

.gallery__text {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-lg);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  animation: fadeIn 0.3s ease-out;
}

.modal__content {
  position: relative;
  margin: auto;
  padding: var(--space-20);
  width: 90%;
  max-width: 800px;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
}

.modal__image {
  width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: var(--radius-lg);
}

.modal__close {
  position: absolute;
  top: 10px;
  right: 25px;
  color: white;
  font-size: 35px;
  font-weight: bold;
  cursor: pointer;
  transition: color var(--duration-fast) var(--ease-standard);
}

.modal__close:hover {
  color: var(--color-primary);
}

.modal__caption {
  color: white;
  font-size: var(--font-size-lg);
  margin-top: var(--space-16);
  font-weight: var(--font-weight-medium);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Category Images */
.category__image {
  margin-top: var(--space-16);
  border-radius: var(--radius-base);
  overflow: hidden;
}

.category__img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.category:hover .category__img {
  transform: scale(1.05);
}

.category {
  cursor: pointer;
  transition: transform var(--duration-normal) var(--ease-standard);
}

.category:hover {
  transform: translateY(-4px);
}

/* WhatsApp Float Button */
.whatsapp-float {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: #25D366;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
  z-index: 1000;
  transition: all var(--duration-normal) var(--ease-standard);
  text-decoration: none;
}

.whatsapp-float:hover {
  background: #128C7E;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
  color: white;
}

.whatsapp-float svg {
  width: 28px;
  height: 28px;
}

/* Business Hours Indicator */
.business-hours {
  font-family: var(--font-family-base);
}

.business-hours .hours-text {
  font-size: 10px;
  opacity: 0.9;
  margin-left: 4px;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 640px) {
  .gallery__grid {
    grid-template-columns: 1fr;
  }

  .gallery__img {
    height: 200px;
  }

  .modal__content {
    width: 95%;
    padding: var(--space-10);
  }

  .modal__image {
    max-height: 60vh;
  }

  .whatsapp-float {
    bottom: 140px;
    right: 15px;
    width: 50px;
    height: 50px;
  }

  .whatsapp-float svg {
    width: 24px;
    height: 24px;
  }

  .business-hours {
    top: 70px;
    right: 10px;
    font-size: 10px;
    padding: 6px 10px;
  }

  .category__img {
    height: 100px;
  }
}

/* Print Styles */
@media print {
  .header,
  .whatsapp-float,
  .business-hours,
  .scroll-to-top,
  .modal {
    display: none !important;
  }

  .hero {
    padding: 20px 0;
    min-height: auto;
  }

  .section {
    padding: 20px 0;
  }

  .gallery__overlay {
    transform: translateY(0);
    position: static;
    background: var(--color-surface);
    color: var(--color-text);
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #000080;
    --color-text: #000000;
    --color-background: #ffffff;
    --color-border: #000000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .gallery__item:hover {
    transform: none;
  }

  .service__card:hover {
    transform: none;
  }
}

/* Focus Improvements for Accessibility */
.gallery__item:focus,
.category:focus {
  outline: 3px solid var(--color-primary);
  outline-offset: 2px;
}

/* Loading States */
.img-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}