# UK Engineering Kitchen Equipments - Cloudflare Pages Deployment Guide

## Overview
This guide will help you deploy the UK Engineering Kitchen Equipments website to Cloudflare Pages with a custom domain for free.

## Prerequisites
- A Cloudflare account (free)
- A domain name (you can purchase one through Cloudflare or use an existing domain)
- The website files (already prepared in this repository)

## Step 1: Prepare Your Files
Your website is ready for deployment with the following structure:
```
uk-engineering-kitchen/
├── index.html
├── style.css
├── app.js
├── img/
│   ├── 0.png
│   ├── 1.png
│   ├── 2.png
│   ├── 3.png
│   ├── 4.png
│   └── 5.png
└── DEPLOYMENT_GUIDE.md
```

## Step 2: Create a Cloudflare Account
1. Go to [cloudflare.com](https://cloudflare.com)
2. Click "Sign Up" and create a free account
3. Verify your email address

## Step 3: Deploy to Cloudflare Pages

### Option A: Direct Upload (Recommended for beginners)
1. Log in to your Cloudflare dashboard
2. Go to "Pages" in the left sidebar
3. Click "Create a project"
4. Choose "Upload assets"
5. Drag and drop all your website files (index.html, style.css, app.js, and img folder)
6. Give your project a name (e.g., "uk-engineering-kitchen")
7. Click "Create project"

### Option B: Connect to GitHub (Recommended for ongoing updates)
1. First, create a GitHub repository:
   - Go to [github.com](https://github.com) and create an account if needed
   - Create a new repository named "uk-engineering-kitchen"
   - Upload all your website files to this repository

2. Connect to Cloudflare Pages:
   - In Cloudflare dashboard, go to "Pages"
   - Click "Create a project"
   - Choose "Connect to Git"
   - Connect your GitHub account
   - Select your "uk-engineering-kitchen" repository
   - Configure build settings:
     - Framework preset: None
     - Build command: (leave empty)
     - Build output directory: /
   - Click "Save and Deploy"

## Step 4: Custom Domain Setup

### If you don't have a domain:
1. In Cloudflare dashboard, go to "Domain Registration"
2. Search for available domains
3. Purchase a domain (prices start from $8-15/year)

### If you have an existing domain:
1. In Cloudflare dashboard, click "Add site"
2. Enter your domain name
3. Choose the Free plan
4. Update your domain's nameservers to Cloudflare's nameservers (provided in the setup)

### Connect your domain to Pages:
1. Go to "Pages" in Cloudflare dashboard
2. Click on your deployed project
3. Go to "Custom domains" tab
4. Click "Set up a custom domain"
5. Enter your domain name
6. Follow the DNS setup instructions

## Step 5: Configure DNS (if using custom domain)
1. In Cloudflare dashboard, go to "DNS"
2. Add the following records:
   - Type: CNAME, Name: www, Content: your-project.pages.dev
   - Type: CNAME, Name: @, Content: your-project.pages.dev

## Step 6: Enable HTTPS and Security
Cloudflare automatically provides:
- Free SSL certificate
- DDoS protection
- CDN (Content Delivery Network)
- Always Online™ feature

## Step 7: Test Your Website
1. Visit your custom domain or the provided .pages.dev URL
2. Test all functionality:
   - Navigation menu
   - Contact form
   - Image gallery
   - WhatsApp button
   - Phone/email links

## Business Features Included

### 1. Quote Request System
- Forms automatically <NAME_EMAIL>
- Customer receives confirmation email option
- Detailed quote information included

### 2. Contact Integration
- Direct phone links for Pasupathy (8220749453) and Kumar (9841470704)
- Email integration
- WhatsApp business integration
- Business hours indicator

### 3. Image Gallery
- Professional product showcase
- Clickable category images
- Modal image viewer
- Mobile-responsive design

### 4. SEO Optimization
- Proper meta tags
- Structured data
- Fast loading times
- Mobile-friendly design

## Ongoing Maintenance

### Updating Content
- If using GitHub: Push changes to your repository, Cloudflare will auto-deploy
- If using direct upload: Re-upload files through Cloudflare Pages dashboard

### Analytics
- Enable Cloudflare Web Analytics in your dashboard
- Monitor visitor traffic and performance

### Performance Optimization
- Cloudflare automatically optimizes images and caching
- Monitor Core Web Vitals in the dashboard

## Support Contacts
- Cloudflare Support: Available 24/7 for free accounts
- Website Issues: Check browser console for errors
- Business Inquiries: Contact UK Engineering directly

## Cost Breakdown
- Cloudflare Pages: Free
- Domain (optional): $8-15/year
- SSL Certificate: Free (included)
- CDN & Security: Free (included)

Your professional website is now ready for deployment with enterprise-grade hosting at no cost!
